import React from 'react'
import { Card } from '@repo/shared/components/ui/card'
import type { RewardSet } from '@repo/shared/features/rewards/types'
import { fetchRewardSets } from '@repo/shared/features/rewards/lib/api'
import RewardSetManager from './RewardSetManager'

export interface RewardSetsManagerProps {
  onSelect?: (set: RewardSet) => void
}

export const RewardSetsManager: React.FC<RewardSetsManagerProps> = ({ onSelect }) => {
  const [sets, setSets] = React.useState<RewardSet[]>([])
  const [loading, setLoading] = React.useState(false)
  const [selectedId, setSelectedId] = React.useState<string | null>(null)

  React.useEffect(() => {
    let mounted = true
    setLoading(true)
    fetchRewardSets()
      .then((x) => (mounted ? setSets(x as RewardSet[]) : void 0))
      .finally(() => mounted && setLoading(false))
    return () => {
      mounted = false
    }
  }, [])

  // Auto-select the first set when loaded
  React.useEffect(() => {
    if (!selectedId && sets.length) {
      setSelectedId(sets[0].id)
    }
  }, [sets, selectedId])

  const selectedSet = React.useMemo(() => sets.find((s) => s.id === selectedId) ?? null, [sets, selectedId])

  const handleSelect = (s: RewardSet) => {
    setSelectedId(s.id)
    onSelect?.(s)
  }

  const handleChange = (next: RewardSet) => {
    setSets((prev) => prev.map((s) => (s.id === next.id ? next : s)))
  }

  return (
    <div className="w-full flex gap-4">
      {/* Left sidebar with mock sets */}
      <div className="w-[240px] shrink-0">
        <div className="text-sm font-medium mb-2">Reward sets</div>
        {loading ? (
          <div className="text-sm text-muted-foreground">Loading…</div>
        ) : sets.length ? (
          <div className="space-y-2">
            {sets.map((s) => (
              <button key={s.id} type="button" onClick={() => handleSelect(s)} className="w-full text-left">
                <Card className={`p-2 ${selectedId === s.id ? 'border-primary' : ''}`}>
                  <div className="flex items-center gap-2">
                    <div className="flex-1 min-w-0">
                      <div className="text-sm truncate">{s.id}</div>
                      <div className="text-xs text-muted-foreground">{s.rewards.length} rewards</div>
                    </div>
                  </div>
                </Card>
              </button>
            ))}
          </div>
        ) : (
          <div className="text-sm text-muted-foreground">No reward sets found.</div>
        )}
      </div>

      {/* Right detail view */}
      <div className="flex-1 min-w-0">
        {selectedSet ? (
          <RewardSetManager set={selectedSet} onChange={handleChange} />
        ) : (
          <div className="text-sm text-muted-foreground">Select a reward set to view details.</div>
        )}
      </div>
    </div>
  )
}

export default RewardSetsManager

