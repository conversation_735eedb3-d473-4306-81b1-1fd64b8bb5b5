import React, { useMemo, useState } from 'react'
import type { RewardDefinition, RewardSet, RewardType } from '@repo/shared/features/rewards/types'
import { Button } from '@repo/shared/components/ui/button'
import { Input } from '@repo/shared/components/ui/input'
import { Label } from '@repo/shared/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@repo/shared/components/ui/select'

export interface RewardSetManagerProps {
  set: RewardSet
  onChange?: (next: RewardSet) => void
}

function genId(prefix = 'reward') {
  return `${prefix}-${Math.random().toString(36).slice(2, 8)}`
}

export const RewardSetManager: React.FC<RewardSetManagerProps> = ({ set, onChange }) => {
  const [local, setLocal] = useState<RewardSet>(set)

  // keep external in sync if parent updates the set prop
  React.useEffect(() => setLocal(set), [set])

  const rewardTypes: RewardType[] = useMemo(() => ['coupon-code', 'claimable-url'], [])

  const [newReward, setNewReward] = useState<Pick<RewardDefinition, 'name' | 'description' | 'type'>>({
    name: '',
    description: '',
    type: rewardTypes[0],
  })

  const handleRemove = (id: string) => {
    const next: RewardSet = { ...local, rewards: local.rewards.filter((r) => r.id !== id) }
    setLocal(next)
    onChange?.(next)
  }

  const handleAdd = () => {
    if (!newReward.name?.trim()) return
    const reward: RewardDefinition = {
      id: genId(),
      name: newReward.name.trim(),
      description: newReward.description?.trim() ?? '',
      type: newReward.type,
    }
    const next: RewardSet = { ...local, rewards: [...(local.rewards || []), reward] }
    setLocal(next)
    onChange?.(next)
    setNewReward({ name: '', description: '', type: rewardTypes[0] })
  }

  return (
    <div className="w-full space-y-4">
      <div className="space-y-2">
        <div className="text-sm font-medium">Rewards</div>
        <div className="space-y-2">
          {local.rewards?.length ? (
            local.rewards.map((r) => (
              <div key={r.id} className="flex items-center gap-2">
                <div className="flex-1 min-w-0">
                  <div className="text-sm truncate">{r.name}</div>
                  {r.description ? (
                    <div className="text-xs text-muted-foreground truncate">{r.description}</div>
                  ) : null}
                  <div className="text-xs text-muted-foreground">{r.type}</div>
                </div>
                <Button variant="ghost" size="sm" onClick={() => handleRemove(r.id)}>Remove</Button>
              </div>
            ))
          ) : (
            <div className="text-sm text-muted-foreground">No rewards in this set.</div>
          )}
        </div>
      </div>

      <div className="space-y-2">
        <div className="text-sm font-medium">Add reward</div>
        <div className="flex flex-col gap-2">
          <div className="flex items-center gap-2">
            <div className="flex-1 min-w-0">
              <Label className="sr-only" htmlFor="reward-name">Name</Label>
              <Input id="reward-name" placeholder="Name" value={newReward.name} onChange={(e) => setNewReward((s) => ({ ...s, name: e.target.value }))} />
            </div>
            <div className="w-[160px]">
              <Label className="sr-only" htmlFor="reward-type">Type</Label>
              <Select value={newReward.type} onValueChange={(v) => setNewReward((s) => ({ ...s, type: v as RewardType }))}>
                <SelectTrigger id="reward-type" className="w-full">
                  <SelectValue placeholder="Type" />
                </SelectTrigger>
                <SelectContent>
                  {rewardTypes.map((t) => (
                    <SelectItem key={t} value={t}>{t}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <Button size="sm" onClick={handleAdd}>Add</Button>
          </div>
          <div>
            <Label className="sr-only" htmlFor="reward-desc">Description</Label>
            <Input id="reward-desc" placeholder="Description (optional)" value={newReward.description} onChange={(e) => setNewReward((s) => ({ ...s, description: e.target.value }))} />
          </div>
        </div>
      </div>
    </div>
  )
}

export default RewardSetManager

